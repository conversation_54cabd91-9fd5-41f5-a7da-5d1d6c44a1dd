/**
 * 编辑器
 * 用来进入编辑模式 操作draw类进行draw
 */

import { Arrow } from 'konva/lib/shapes/Arrow';
import { Image } from 'konva/lib/shapes/Image';
import OpenSeadragon, { Point, Rect } from 'openseadragon';
import { MouseTracker } from 'openseadragon';
import { Drawer } from './drawer';
import { DrawStatus, DrawType } from './editorEnum';
import { Layer } from './konvaOverlayer';
import Label from './label';
import { changeArrowPointerByZoom, isMouseLeftOrTouch, changeFlagSizeByZoom } from '@/utils/index';
import { OpenSeadragonRatio } from '@/views/Drawers/Drawer.types';
import Konva from 'konva';
import { Line } from 'konva/lib/shapes/Line';
import { Group } from 'konva/lib/Group';

//绘画点
export interface DrawPoint {
  x: number;
  y: number;
  imageX: number;
  imageY: number;
  pcx?: number;
  pcy?: number;
}

//标记
export class Marker {
  Area?: number;
  Coordinates?: any;
  Description?: string;
  FontColor?: string;
  FontSize?: number;
  ID!: string;
  LineColor?: string;
  LineWidth?: number;
  Name!: string;
  PartOfGroup?: string;
  Type?: string;
  attrs?: any;
  remarkValue?: any;
}

export class Editor {
  _layer: Layer; //画图层
  _mouseTracker: MouseTracker; //鼠标（指针设备）追踪器
  _drawType!: DrawType | null; //绘画形状类型
  _drawer: Drawer; //绘图器
  _viewer: OpenSeadragon.Viewer;
  _drawOptions: any;
  _isStart: boolean = false;
  _label: Label;
  _isMoving = false;
  _timer: any = null;
  _isDragging: boolean = false;
  currentGraph: any;
  private _ratio?: OpenSeadragonRatio;
  private _startCallback: Function | null = null;
  private _closeCallback: Function | null = null;
  private _options: any = null;
  //缩放倍率
  get scale(): number {
    return this._layer ? this._layer.scaleX() : 1;
  }
  get scale1(): number {
    return this._layer ? this._layer.scaleY() : 1;
  }
  //Konva.Stage 是树的根节点
  get stage() {
    return this._layer.getStage();
  }

  //画纸容器
  get canvasDiv() {
    return this._layer.getStage().container();
  }

  //判断是否有绘图器
  get hasDrawer(): boolean {
    return this._drawer ? true : false;
  }
  //判断是否有绘图器
  get drawer(): Drawer | null {
    return this._drawer || null;
  }

  //视图
  get viewport(): OpenSeadragon.Viewport {
    return this._viewer.viewport;
  }

  //绘图状态
  get status(): DrawStatus {
    return this._drawer.status;
  }

  //
  get mouseTracker(): MouseTracker {
    return this._mouseTracker;
  }
  constructor(viewer: OpenSeadragon.Viewer, layer: Layer, options: any) {
    this._viewer = viewer;
    this._layer = layer;
    this._mouseTracker = new OpenSeadragon.MouseTracker({
      element: this.canvasDiv,
      startDisabled: true,
    });
    this._drawer = new Drawer({
      layer,
      viewer,
      needCountArea: options.needCountArea,
      Label: options.Label,
    });

    // 监听工具完成事件
    this._drawer.on('toolComplete', (result: any) => {
      if (result.type === 'wand') {
        this.close();
      }
    });

    this.addListener();
    this._label = options.Label;
    this._options = options;
    // this._setESCClose();
  }
  customMouseEvent(position: { x: number; y: number }) {
    const cb = (
      (
        document.getElementById(this._viewer?.element?.parentElement!.id) as HTMLElement
      ).querySelector('.konvajs-content') as any
    ).getElementsByTagName('canvas')[0];
    const el = (
      document.getElementById(this._viewer?.element?.parentElement!.id) as HTMLElement
    ).querySelector('#konva-overlay');
    const resultPosition = {
      // 问题出在这里 多了侧边栏200px和上边的100px
      x: position.x + el!.getBoundingClientRect().left,
      y: position.y + el!.getBoundingClientRect().top,
    };
    let evt = new MouseEvent('mousedown', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: resultPosition.x,
      clientY: resultPosition.y,
    });
    cb.dispatchEvent(evt);
    evt = new MouseEvent('mouseup', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: resultPosition.x,
      clientY: resultPosition.y,
    });
    cb.dispatchEvent(evt);
  }

  addListener() {
    this._viewer?.addHandler('zoom', (ev) => {
      const zoom = this._viewer.viewport.getZoom();

      // 箭头的缩放
      const arrows = this._layer.getChildren((node: any) => {
        return node.attrs.name === 'arrow';
      });
      if (arrows?.length > 0) {
        arrows.forEach((arrow) => {
          changeArrowPointerByZoom(arrow as Arrow, zoom);
        });
      }

      // 标旗的缩放
      const flags = this._layer.getChildren((node: any) => {
        return node.attrs.name === 'flag';
      });
      if (flags?.length > 0) {
        flags.forEach((flag) => {
          changeFlagSizeByZoom(flag as Image, zoom, this._viewer.viewport);
        });
      }
      this._drawer.drawZoom({
        handler: 'zoom',
        type: this._drawType,
        ev,
        config: this._drawOptions,
      });
    });

    // PC
    this._viewer?.addHandler('canvas-click', (ev) => {
      this.customMouseEvent({
        x: ev.position?.x as number,
        y: ev.position?.y as number,
      });
    });

    // 监听拖拽开始
    this._viewer?.addHandler('canvas-drag', (ev) => {
      this._isDragging = true;
      clearTimeout(this._timer);
    });

    // 监听拖拽结束
    this._viewer?.addHandler('canvas-release', (ev) => {
      if (this._isDragging) {
        this._timer = setTimeout(() => {
          this._isDragging = false;
        }, 0);
      }
    });

    // 执行顺序 down  up  click  有了move之后就是down click up?
    this.stage.on('click tap', (ev) => {
      // 在mouseup之后触发
      // console.log('click tap', this.status, ev, this._isDragging);
      console.log('node', this.drawer?.node);
      var pos = this.stage.getPointerPosition();
      if (this.status === DrawStatus.drawing) return;
      if (this.status === DrawStatus.select) {
        this._drawer.cancelSelect();
        if (!ev.target.attrs.id) {
          return this.close();
        }
      }

      // 尺子和同心圆的相关判断
      if (
        ev.target.attrs?.tName?.includes('ruler') ||
        ev.target.attrs?.tName?.includes('Ruler') ||
        ev.target.attrs?.tName?.includes('ring') ||
        ev.target.attrs?.tName?.includes('Ring')
      ) {
        if (this._isStart) {
          this.close();
        }
        !this._isStart && this.start();
        ev.target?.parent?.find((node: any) => {
          if (node.attrs.tName.includes('Anchor')) {
            node.setAttr('visible', true);
          }
        });
        ev.target?.parent?.setAttrs({
          isSelect: true,
        });
        if (ev.target.parent?.attrs.name === DrawType.ruler) {
          ev.target?.parent?.setAttrs({
            draggable: true,
          });
        }

        return;
      }

      if (ev.target === this.stage) {
        const trs = this.stage.find('Transformer');
        for (const tr of trs) {
          this._drawer.setStatus(DrawStatus.end);
          tr.destroy();
        }
        this._layer.draw();
        return;
      }

      // console.log('hello wrold', ev.target, this._drawer.node)
      // if (ev.target === this._drawer.node) {
      //   return
      // }
      // if (ev.target.attrs?.name.includes('Group')) {
      //   !this._isStart && this.start();
      //   this._drawer.select(ev.target.parent);
      //   return;
      // }

      // ev.cancelBubble = true;

      // !this._isDragging && !this._isStart && this.start();
      // !this._isDragging && this._drawer.select(ev.target);
      !this._isStart && this.start();
      this._drawer.select(ev.target);
    });

    this.stage.on('contextmenu', (ev: any) => {
      if (this.status === DrawStatus.select) return;
      ev.evt.preventDefault();
      this._drawer.drawContextmenutHandler({
        handler: 'contextmenu',
        type: this._drawType,
        ev,
        config: this._drawOptions,
      });
    });

    this.stage.on('touchstart mousedown', (ev: any) => {
      console.log('mousedown', this.status, 'state', ev);
      if (this.status === DrawStatus.select) return;
      if (!isMouseLeftOrTouch(ev)) return;
      this._drawer.drawStartHandler({
        handler: 'mousedown',
        type: this._drawType,
        ev,
        config: this._drawOptions,
      });
    });

    this.stage.on('touchmove mousemove', (ev) => {
      // console.log('touchmove', this.status, 'state');
      this._isMoving = true;

      // 处理可拖拽的anchor
      // if (this.status !== DrawStatus.drawing) {
      //   if (ev.target.attrs?.name?.includes('Anchor')) {
      //     !this._isStart && this.start(undefined, 'move');
      //     this._drawer.setStatus(DrawStatus.anchorStart);
      //     return;
      //   } else {
      //     // this._isStart && this.close();
      //   }
      // }
      // if (this.status === DrawStatus.anchorStart) {
      //   this._isStart && this.close();
      // }

      if (this.status === DrawStatus.select || this.status === DrawStatus.end) return;
      if (this.status === DrawStatus.drawing) {
        this._drawer.drawMoveHandler({
          handler: 'mousemove',
          type: this._drawType,
          ev,
          config: this._drawOptions,
        });
      }
    });

    // click dblclick 都在mouseup之前触发
    this.stage.on('touchend mouseup', (ev) => {
      // console.log('mouseup', this.status, 'state', ev);
      if (!isMouseLeftOrTouch(ev)) return;

      // 处理ruler的拖动不取消编辑状态
      if (
        this.status === DrawStatus.end &&
        ev.target.attrs?.tName?.includes('Ruler') &&
        ev.target.attrs?.tName?.includes('Anchor')
      ) {
        return this._label.setLabelsByLayer(this._layer);
      }

      // 处理Ring的拖动不取消编辑状态
      if (
        this.status === DrawStatus.end &&
        ev.target.attrs?.tName?.includes('Ring') &&
        ev.target.attrs?.tName?.includes('Anchor')
      ) {
        return this._label.setLabelsByLayer(this._layer);
      }
      // close之后

      if (this.status === DrawStatus.end) {
        if (this._isStart) {
          this.close();
        }
        return;
      }
      if (this._drawType === DrawType.arrow && this._drawer._node && !this._isMoving) {
        this.close();
      }
      this._isMoving = false;
      if (this.status === DrawStatus.select) {
        this._drawer.updateHistory();
        // 在这里对attrs其他属性进行改变
        return this._label.setLabelsByLayer(this._layer);
      }
      this._drawer.drawEndHandler({
        handler: 'mouseup',
        type: this._drawType,
        ev: ev,
        config: this._drawOptions,
      });
    });

    // 双击退出编辑模式
    this.stage.on('dblclick dbltap', (ev) => {
      if (!isMouseLeftOrTouch(ev)) return;
      if (this.status !== DrawStatus.select) {
        this._drawer.drawDblClick({
          handler: 'dblclick',
          type: this._drawType,
          ev,
          config: this._drawOptions,
        });
      }
      console.log(this._isStart, 'this._isStart');
      if (this._isStart) {
        if (this._drawType !== DrawType.foldLine && this._drawType !== DrawType.polygon) {
          this.close();
        }
      } else {
        // !this._isMoving && this.start();
      }
    });

    // 鼠标滚动退出编辑模式
    this.stage.on('wheel', (ev) => {
      if (this.status !== DrawStatus.drawing) {
        this.close();
      }
    });
  }

  setLabelsByLayer() {
    this._label.setLabelsByLayer(this._layer);
  }

  setAttr(id: string, config: any) {
    const item = this.getItemById(id);
    const { name } = item?.attrs;
    const { strokeWidth } = config;
    if (name === DrawType.flag) {
      delete config.stroke;
    }
    if (name === DrawType.ruler && config.stroke) {
      (item as Group).find((node: any) => {
        const { tName } = node.attrs;
        if (tName === 'Group_Ruler_Line_Base') {
          node.setAttrs({
            ...config,
          });
        }
      });
      return;
    }
    item?.setAttrs?.({
      ...config,
    });
  }

  //编辑标注 needCancel如果是同一个是否需要取消选中
  editMark(id: string, needCancel = true) {
    const item = this.getItemById(id);
    const visible = this.markIsVisible(id);
    if (!visible) return;
    console.log(this.status, 'this.status');
    if (this.status === DrawStatus.select) {
      // 点的是另一个shape
      if (this._drawer.node !== item) {
        this.start();
        this._drawer.select(item);
        return;
      }
      this.close();
      needCancel && this._drawer.cancelSelect();
    } else {
      this.start();
      this._drawer.select(item);
    }
  }

  markIsVisible(id: string) {
    const item = this.getItemById(id);
    return item?.isVisible();
  }
  toggleMark(id: string) {
    const item = this.getItemById(id);
    const visible = item?.isVisible();
    this._drawer.cancelSelect();
    if (!visible) {
      item?.show();
    } else {
      item?.hide();
    }
  }
  // 隐藏标注
  hiddenMark(id: string) {
    const item = this.getItemById(id);
    this._drawer.cancelSelect();
    item?.hide();
  }

  // 展示标注
  showMark(id: string) {
    const item = this.getItemById(id);
    this._drawer.cancelSelect();
    item?.show();
  }
  // 销毁标注
  destroyMark(id: string) {
    if (this.status === DrawStatus.select) {
      this.close();
      this._drawer.cancelSelect();
    }
    const item = this.getItemById(id);
    item?.destroy();
  }

  // 显示所有标注
  showAllItems() {
    this._layer.show();
  }

  // 隐藏所有标注
  hideAllItems() {
    this._layer.hide();
  }

  /**
   * 显示attrs有key且值为value的所有标注
   */
  showGroup(key: string, value: string | number) {
    const childs = this._layer.getChildren((node) => {
      return node.attrs[key] === value;
    });
    childs.forEach((child) => {
      child.show();
    });
  }

  /**
   * 隐藏attrs有key且值为value的所有标注
   */
  hideGroup(key: string, value: string | number) {
    const childs = this._layer.getChildren((node) => {
      return node.attrs[key] === value;
    });
    childs.forEach((child) => {
      child.hide();
    });
  }

  // 批量删除标注
  destroyMarkers(fn: Function) {
    const childs =
      this._layer.getChildren((node) => {
        return fn(node);
      }) || [];
    const ids = childs.map((child) => child.attrs.id);
    childs.forEach((child) => {
      child.destroy();
    });
    this._label.delLabels(ids);
  }

  // 根据id获取标注
  getItemById(id: string) {
    const childrens = this._layer.getChildren((node) => {
      return node.id() === id;
    });
    if (childrens.length === 0) {
      return console.error('未找到id为' + id + '的标注');
    } else if (childrens.length === 1) {
      return childrens[0];
    } else {
      return console.error('id为' + id + '的标注重复');
    }
  }

  // 隐藏layer
  showLayer(id: string) {
    this._layer.show();
  }

  hiddenLayer(id: string) {
    this._layer.hide();
  }

  pixlFromKonvaPoint(point: { x: number; y: number }) {
    const { x, y } = point;
    // x = (pixel.x - pixelNoRotate.x) / this.layerScale  --- 这是将鼠标坐标转为Konva坐标点的公式
    // 求pixel.x公式：pixel.x = x * scale + pixelNoRotate.x
    const pixelNoRotate = this._viewer.viewport.pixelFromPointNoRotate(
      new OpenSeadragon.Point(0, 0),
      true
    );

    const scaleX = this._layer.scaleX();
    const scaleY = this._layer.scaleY();
    const pixelX = x * scaleX + pixelNoRotate.x;
    const pixelY = y * scaleY + pixelNoRotate.y;
    return this._viewer.viewport.pointFromPixel(new Point(pixelX, pixelY));
    // ！重点是算出形状在OSD上的Point
  }

  // 定位
  positioningMark(id: string) {
    const item = this.getItemById(id);
    if (item) {
      let positionX = undefined;
      let positionY = undefined;
      const { x, y, width, height, name, points, zoom, radiusX, radiusY, tName } = item.attrs;
      if (name === DrawType.rect) {
        positionX = x + width / 2;
        positionY = y + height / 2;
      } else if (name === DrawType.circle) {
        positionX = x;
        positionY = y;
      } else if (name === DrawType.line) {
        positionX = (points[0] + points[2]) / 2;
        positionY = (points[1] + points[3]) / 2;
      } else if (name === DrawType.arrow) {
        positionX = x + points[2] / 2;
        positionY = y + points[3] / 2;
      } else if (name === DrawType.ellipse) {
        positionX = x - radiusX / 2;
        positionY = y - radiusY / 2;
      } else if (tName?.includes('Group')) {
        if (tName === 'Group_Ring') {
          const childs = (item as Konva.Group).find((node: any) => {
            return node.attrs.tName === 'Group_Ring_Ring';
          });
          if (childs.length === 1) {
            positionX = childs[0].attrs.x;
            positionY = childs[0].attrs.y;
          } else {
            positionX = 0;
            positionY = 0;
            console.error('无法获取到正确的shape');
          }
        } else if (tName === 'Group_Ruler') {
          const childs = (item as Konva.Group).find((node: any) => {
            return node.attrs.tName === 'Group_Ruler_Line_Base';
          });
          if (childs.length === 1) {
            const points = (childs[0] as Line).points();
            positionX = (points[0] + points[2]) / 2;
            positionY = (points[1] + points[3]) / 2;
          } else {
            positionX = 0;
            positionY = 0;
            console.error('无法获取到正确的shape');
          }
        }
      } else if (points) {
        positionX = points[0];
        positionY = points[1];
        if (x === 0 && y === 0 && x && y) {
          positionX = x;
          positionY = y;
        }
      } else if (x) {
        positionX = x;
        positionY = y;
      } else {
        positionX = 0;
        positionY = 0;
      }
      const point = this.pixlFromKonvaPoint({ x: positionX, y: positionY });
      this._viewer.viewport.panTo(point);
      //移动到指定缩放倍率
      this._viewer.viewport.zoomTo(zoom || 1);
      const needEdit = name !== DrawType.ruler;
      needEdit && this.editMark(id, false);
    }
  }
  startCallback(cb: Function) {
    this._startCallback = cb;
  }
  closeCallback(cb: Function) {
    this._closeCallback = cb;
  }
  // 开始画图模式
  start(drawType?: DrawType, cursorType = 'crosshair') {
    this._isStart = true;
    this._mouseTracker.setTracking(true);
    this._viewer.setMouseNavEnabled(false);
    this.stage.container().style.cursor = cursorType;
    this._drawer.cancelSelect();
    if (drawType != undefined) {
      this.setDrawType(drawType);
    }
    if (this.canvasDiv) {
      this.canvasDiv.style.zIndex = '2';
    }
    this._startCallback?.();
  }
  //关闭编辑模式
  close() {
    this._isStart = false;
    this._drawer.cancelSelect();
    this._drawer._nodeDestroy();
    if (this._mouseTracker) {
      this.stage.container().style.cursor = 'default';
      this._mouseTracker.setTracking(false);
      this._viewer.setMouseNavEnabled(true);
    }
    if (this.canvasDiv) {
      this.canvasDiv.style.zIndex = '1';
    }
    this._closeCallback?.();
    this._drawType = null;

    const groupChilds = this._layer.find('Group');
    if (groupChilds) {
      groupChilds.forEach((item: any) => {
        item.setAttrs({
          draggable: false,
          isSelect: false,
        });
        item.find((node: any) => {
          if (node.attrs.tName?.includes('Anchor')) {
            node.setAttr('visible', false);
            node.setAttr('isSelect', false);
          }
        });
      });
    }
  }

  // 修改当前选中shape的样式
  setDrawingOption(drawOptions: any) {
    this._drawOptions = drawOptions;
  }

  //设置类型
  setDrawType(drawType: DrawType) {
    this._drawType = drawType;
  }

  setRotate(roa: number) {
    this.stage.to({ scaleX: -this.stage.scaleX() });
  }

  setRatio(scale: OpenSeadragonRatio) {
    this._drawer.setRatio(scale);
    this._ratio = scale;
  }

  //添加标注
  addMarker(markers: any): void {
    return this._drawer.addMarker(markers);
  }

  //清除画布的子元素
  clear() {
    this._layer.removeChildren();
  }

  _setESCClose() {
    window.addEventListener('keydown', (ev: any) => {
      const { keyCode, shiftKey } = ev;
      if (keyCode === 27) {
        if (this._isStart) {
          this.close();
        }
      }
    });
  }
}
